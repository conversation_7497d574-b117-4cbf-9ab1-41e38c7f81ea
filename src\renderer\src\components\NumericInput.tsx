/**
 * Numeric Input Component
 * Specialized input component for numeric values with validation and styling
 */

import React, { useCallback, useRef } from 'react'
import type { NumericInputProps } from '../../../shared/types/trading'
import { CSS_CLASSES } from '../../../shared/constants/trading'

/**
 * NumericInput Component
 * Handles numeric input with proper validation, formatting, and accessibility
 */
export const NumericInput: React.FC<NumericInputProps> = ({
  label,
  value,
  onChange,
  placeholder,
  error,
  required = false,
  min,
  max,
  step = 0.01,
  ariaLabel
}) => {
  const inputRef = useRef<HTMLInputElement>(null)

  /**
   * <PERSON>les input change with numeric validation
   */
  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = event.target.value

      // Allow empty string for clearing the input
      if (inputValue === '') {
        onChange('')
        return
      }

      // Allow partial numeric input (including decimal point)
      const numericRegex = /^-?\d*\.?\d*$/
      if (numericRegex.test(inputValue)) {
        onChange(inputValue)
      }
    },
    [onChange]
  )

  /**
   * Handles input blur to format the value
   */
  const handleInputBlur = useCallback(() => {
    if (value && !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value)
      // Format to 2 decimal places for currency
      const formattedValue = numValue.toFixed(2)
      if (formattedValue !== value) {
        onChange(formattedValue)
      }
    }
  }, [value, onChange])

  /**
   * Handles keyboard navigation
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow: backspace, delete, tab, escape, enter, home, end, left, right, up, down
    const allowedKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      'Home',
      'End',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown'
    ]

    if (allowedKeys.includes(event.key)) {
      return
    }

    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+Z
    if (event.ctrlKey && ['a', 'c', 'v', 'x', 'z'].includes(event.key.toLowerCase())) {
      return
    }

    // Allow: numbers, decimal point, minus sign
    if (!/[\d.-]/.test(event.key)) {
      event.preventDefault()
    }
  }, [])

  /**
   * Generates unique IDs for accessibility
   */
  const inputId = `numeric-input-${label.toLowerCase().replace(/\s+/g, '-')}`
  const errorId = `${inputId}-error`

  return (
    <div className={`${CSS_CLASSES.inputGroup} space-y-2`}>
      {/* Label */}
      <label htmlFor={inputId} className="block text-sm font-medium text-gray-200">
        {label}
        {required && (
          <span className="text-red-400 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>

      {/* Input Container */}
      <div className="relative">
        <input
          ref={inputRef}
          id={inputId}
          type="text"
          inputMode="decimal"
          value={value}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          min={min}
          max={max}
          step={step}
          required={required}
          aria-label={ariaLabel || label}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={!!error}
          className={`
            ${CSS_CLASSES.input}
            w-full
            pl-6
            pr-1
            py-0.5
            text-sm
            bg-gray-800
            border
            rounded
            text-gray-100
            placeholder-gray-400
            focus:outline-none
            focus:ring-2
            focus:ring-blue-500
            focus:border-transparent
            transition-colors
            duration-200
            ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 hover:border-gray-500'}
          `}
          style={{
            paddingTop: '2px',
            paddingBottom: '2px',
            paddingLeft: '20px',
            paddingRight: '4px',
            borderRadius: '3px',
            maxWidth: '200px' // Optimized for numeric values up to 9,999,999.00
          }}
        />

        {/* Currency Symbol */}
        <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm pointer-events-none">
          $
        </span>
      </div>

      {/* Error Message */}
      {error && (
        <p
          id={errorId}
          className={`${CSS_CLASSES.errorMessage} text-sm text-red-400`}
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  )
}

export default NumericInput
