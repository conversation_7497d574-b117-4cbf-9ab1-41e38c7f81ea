/**
 * TypeScript type definitions for Trading Bot UI
 * Contains all types and interfaces used by the trading bot components
 */

/**
 * Trade expiry duration options
 */
export type TradeExpiryDuration = 'S5' | 'S15' | 'S30' | 'M1' | 'M3' | 'M5' | 'H1' | 'H4' | 'H5';

/**
 * Trading bot state
 */
export type TradingBotState = 'stopped' | 'running';

/**
 * Interface for trading bot configuration
 */
export interface TradingBotConfig {
  /** Total pot money available for trading */
  tradeCapital: number;
  /** Desired profit target for the auto trading session */
  targetProfit: number;
  /** Amount to be used per individual trade */
  tradeAmount: number;
  /** Duration selection for trades */
  tradeExpiryDuration: TradeExpiryDuration;
}

/**
 * Interface for trading bot form data with validation
 */
export interface TradingBotFormData {
  tradeCapital: string;
  targetProfit: string;
  tradeAmount: string;
  tradeExpiryDuration: TradeExpiryDuration | null;
}

/**
 * Interface for form validation errors
 */
export interface TradingBotFormErrors {
  tradeCapital?: string;
  targetProfit?: string;
  tradeAmount?: string;
  tradeExpiryDuration?: string;
}

/**
 * Interface for trading bot UI state
 */
export interface TradingBotUIState {
  /** Current bot state */
  state: TradingBotState;
  /** Form data */
  formData: TradingBotFormData;
  /** Form validation errors */
  errors: TradingBotFormErrors;
  /** Whether the form is valid */
  isValid: boolean;
}

/**
 * Props for numeric input components
 */
export interface NumericInputProps {
  /** Input label */
  label: string;
  /** Input value */
  value: string;
  /** Change handler */
  onChange: (value: string) => void;
  /** Placeholder text */
  placeholder?: string;
  /** Error message */
  error?: string;
  /** Whether the input is required */
  required?: boolean;
  /** Minimum value */
  min?: number;
  /** Maximum value */
  max?: number;
  /** Step value for increment/decrement */
  step?: number;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Props for trade expiry duration selector
 */
export interface TradeExpiryDurationSelectorProps {
  /** Currently selected duration */
  selectedDuration: TradeExpiryDuration | null;
  /** Change handler */
  onChange: (duration: TradeExpiryDuration) => void;
  /** Error message */
  error?: string;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Props for start/stop button
 */
export interface StartStopButtonProps {
  /** Current bot state */
  state: TradingBotState;
  /** Click handler */
  onClick: () => void;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

/**
 * Props for the main trading bot UI component
 */
export interface TradingBotUIProps {
  /** Initial configuration (optional) */
  initialConfig?: Partial<TradingBotConfig>;
  /** Callback when bot starts */
  onStart?: (config: TradingBotConfig) => void;
  /** Callback when bot stops */
  onStop?: () => void;
  /** Callback when configuration changes */
  onConfigChange?: (config: Partial<TradingBotConfig>) => void;
}
