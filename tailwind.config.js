/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/renderer/index.html', './src/renderer/src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Custom colors from the existing CSS variables
        'ev-white': '#ffffff',
        'ev-white-soft': '#f8f8f8',
        'ev-white-mute': '#f2f2f2',
        'ev-black': '#1b1b1f',
        'ev-black-soft': '#222222',
        'ev-black-mute': '#282828',
        'ev-gray-1': '#515c67',
        'ev-gray-2': '#414853',
        'ev-gray-3': '#32363f',
        'ev-text-1': 'rgba(255, 255, 245, 0.86)',
        'ev-text-2': 'rgba(235, 235, 245, 0.6)',
        'ev-text-3': 'rgba(235, 235, 245, 0.38)'
      },
      fontFamily: {
        sans: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Oxygen',
          'Ubuntu',
          'Cantarell',
          'Fira Sans',
          'Droid Sans',
          'Helvetica Neue',
          'sans-serif'
        ]
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'
      },
      boxShadow: {
        'glow-green': '0 0 20px rgba(34, 197, 94, 0.5)',
        'glow-red': '0 0 20px rgba(239, 68, 68, 0.5)',
        'glow-blue': '0 0 20px rgba(59, 130, 246, 0.5)'
      }
    }
  }
}
