/**
 * Trading Bot Constants
 * Contains all constants used by the trading bot UI and functionality
 */

import type { TradeExpiryDuration } from '../types/trading';

/**
 * Trade expiry duration options with display labels
 */
export const TRADE_EXPIRY_DURATIONS: Array<{
  value: TradeExpiryDuration;
  label: string;
  description: string;
}> = [
  { value: 'S5', label: 'S5', description: '5 seconds' },
  { value: 'S15', label: 'S15', description: '15 seconds' },
  { value: 'S30', label: 'S30', description: '30 seconds' },
  { value: 'M1', label: 'M1', description: '1 minute' },
  { value: 'M3', label: 'M3', description: '3 minutes' },
  { value: 'M5', label: 'M5', description: '5 minutes' },
  { value: 'H1', label: 'H1', description: '1 hour' },
  { value: 'H4', label: 'H4', description: '4 hours' },
  { value: 'H5', label: 'H5', description: '5 hours' }
] as const;

/**
 * Default trading bot configuration values
 */
export const DEFAULT_TRADING_CONFIG = {
  tradeCapital: 1000,
  targetProfit: 100,
  tradeAmount: 10,
  tradeExpiryDuration: 'M1' as TradeExpiryDuration
} as const;

/**
 * Input validation constraints
 */
export const VALIDATION_CONSTRAINTS = {
  tradeCapital: {
    min: 1,
    max: 9999999,
    step: 0.01
  },
  targetProfit: {
    min: 0.01,
    max: 9999999,
    step: 0.01
  },
  tradeAmount: {
    min: 0.01,
    max: 9999999,
    step: 0.01
  }
} as const;

/**
 * UI styling constants
 */
export const UI_CONSTANTS = {
  input: {
    paddingVertical: '2px',
    paddingHorizontal: '4px',
    borderRadius: '3px'
  },
  button: {
    borderRadius: '50%', // Circular button
    glowColor: '#6988e6',
    glowIntensity: '0 0 1.2em'
  }
} as const;

/**
 * Form field labels and placeholders
 */
export const FORM_LABELS = {
  tradeCapital: {
    label: 'Trade Capital',
    placeholder: 'Enter total pot money',
    ariaLabel: 'Trade capital amount in dollars'
  },
  targetProfit: {
    label: 'Target Profit (Take Profit)',
    placeholder: 'Enter desired profit target',
    ariaLabel: 'Target profit amount in dollars'
  },
  tradeAmount: {
    label: 'Trade Amount',
    placeholder: 'Enter amount per trade',
    ariaLabel: 'Trade amount per individual trade in dollars'
  },
  tradeExpiryDuration: {
    label: 'Trade Expiry Duration',
    ariaLabel: 'Select trade expiry duration'
  },
  startStopButton: {
    start: 'Start Bot',
    stop: 'Stop Trading',
    ariaLabel: 'Toggle trading bot state'
  }
} as const;

/**
 * Error messages for form validation
 */
export const ERROR_MESSAGES = {
  required: 'This field is required',
  invalidNumber: 'Please enter a valid number',
  minValue: (min: number) => `Value must be at least ${min}`,
  maxValue: (max: number) => `Value must not exceed ${max}`,
  tradeCapital: {
    required: 'Trade capital is required',
    invalid: 'Please enter a valid trade capital amount',
    tooLow: 'Trade capital must be at least $1.00',
    tooHigh: 'Trade capital cannot exceed $9,999,999.00'
  },
  targetProfit: {
    required: 'Target profit is required',
    invalid: 'Please enter a valid target profit amount',
    tooLow: 'Target profit must be at least $0.01',
    tooHigh: 'Target profit cannot exceed $9,999,999.00'
  },
  tradeAmount: {
    required: 'Trade amount is required',
    invalid: 'Please enter a valid trade amount',
    tooLow: 'Trade amount must be at least $0.01',
    tooHigh: 'Trade amount cannot exceed $9,999,999.00',
    exceedsCapital: 'Trade amount cannot exceed trade capital'
  },
  tradeExpiryDuration: {
    required: 'Please select a trade expiry duration'
  }
} as const;

/**
 * CSS class names for styling
 */
export const CSS_CLASSES = {
  container: 'trading-bot-container',
  leftColumn: 'trading-bot-left-column',
  rightColumn: 'trading-bot-right-column',
  inputGroup: 'trading-bot-input-group',
  input: 'trading-bot-input',
  inputError: 'trading-bot-input-error',
  durationSelector: 'trading-bot-duration-selector',
  durationButton: 'trading-bot-duration-button',
  durationButtonActive: 'trading-bot-duration-button-active',
  startStopButton: 'trading-bot-start-stop-button',
  startStopButtonRunning: 'trading-bot-start-stop-button-running',
  errorMessage: 'trading-bot-error-message'
} as const;
