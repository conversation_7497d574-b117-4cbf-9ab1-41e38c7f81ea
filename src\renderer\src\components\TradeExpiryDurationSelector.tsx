/**
 * Trade Expiry Duration Selector Component
 * Provides clickable buttons for selecting trade expiry duration with radio button behavior
 */

import React, { useCallback } from 'react';
import type { TradeExpiryDurationSelectorProps, TradeExpiryDuration } from '../../../shared/types/trading';
import { TRADE_EXPIRY_DURATIONS, CSS_CLASSES } from '../../../shared/constants/trading';

/**
 * TradeExpiryDurationSelector Component
 * Renders a grid of clickable buttons for duration selection
 */
export const TradeExpiryDurationSelector: React.FC<TradeExpiryDurationSelectorProps> = ({
  selectedDuration,
  onChange,
  error,
  ariaLabel
}) => {
  /**
   * Handles duration button click
   */
  const handleDurationClick = useCallback((duration: TradeExpiryDuration) => {
    onChange(duration);
  }, [onChange]);

  /**
   * Handles keyboard navigation for duration buttons
   */
  const handleKeyDown = useCallback((
    event: React.KeyboardEvent<HTMLButtonElement>,
    duration: TradeExpiryDuration,
    index: number
  ) => {
    const buttons = document.querySelectorAll('[data-duration-button]');
    
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        onChange(duration);
        break;
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = (index + 1) % buttons.length;
        (buttons[nextIndex] as HTMLButtonElement)?.focus();
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = (index - 1 + buttons.length) % buttons.length;
        (buttons[prevIndex] as HTMLButtonElement)?.focus();
        break;
      case 'Home':
        event.preventDefault();
        (buttons[0] as HTMLButtonElement)?.focus();
        break;
      case 'End':
        event.preventDefault();
        (buttons[buttons.length - 1] as HTMLButtonElement)?.focus();
        break;
    }
  }, [onChange]);

  const selectorId = 'trade-expiry-duration-selector';
  const errorId = `${selectorId}-error`;

  return (
    <div className={`${CSS_CLASSES.durationSelector} space-y-2`}>
      {/* Label */}
      <fieldset>
        <legend className="block text-sm font-medium text-gray-200 mb-3">
          Trade Expiry Duration
          <span className="text-red-400 ml-1" aria-label="required">*</span>
        </legend>

        {/* Duration Buttons Grid */}
        <div 
          className="grid grid-cols-3 gap-2"
          role="radiogroup"
          aria-label={ariaLabel || "Select trade expiry duration"}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={!!error}
        >
          {TRADE_EXPIRY_DURATIONS.map((duration, index) => {
            const isSelected = selectedDuration === duration.value;
            
            return (
              <button
                key={duration.value}
                type="button"
                data-duration-button
                onClick={() => handleDurationClick(duration.value)}
                onKeyDown={(e) => handleKeyDown(e, duration.value, index)}
                role="radio"
                aria-checked={isSelected}
                aria-label={`${duration.label} - ${duration.description}`}
                tabIndex={isSelected ? 0 : -1}
                className={`
                  ${CSS_CLASSES.durationButton}
                  ${isSelected ? CSS_CLASSES.durationButtonActive : ''}
                  relative
                  px-3
                  py-2
                  text-sm
                  font-medium
                  rounded
                  border
                  transition-all
                  duration-200
                  focus:outline-none
                  focus:ring-2
                  focus:ring-blue-500
                  focus:ring-offset-2
                  focus:ring-offset-gray-900
                  ${isSelected
                    ? 'bg-blue-600 border-blue-500 text-white shadow-lg'
                    : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700 hover:border-gray-500'
                  }
                  ${error ? 'border-red-500' : ''}
                `}
                style={{
                  borderRadius: '3px'
                }}
              >
                {/* Duration Label */}
                <span className="block font-semibold">
                  {duration.label}
                </span>
                
                {/* Duration Description */}
                <span className={`block text-xs ${isSelected ? 'text-blue-100' : 'text-gray-400'}`}>
                  {duration.description}
                </span>

                {/* Selected Indicator */}
                {isSelected && (
                  <div className="absolute top-1 right-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </button>
            );
          })}
        </div>

        {/* Instructions */}
        <p className="text-xs text-gray-400 mt-2">
          Use arrow keys to navigate, Enter or Space to select
        </p>
      </fieldset>

      {/* Error Message */}
      {error && (
        <p 
          id={errorId}
          className={`${CSS_CLASSES.errorMessage} text-sm text-red-400`}
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  );
};

export default TradeExpiryDurationSelector;
